"""
<PERSON><PERSON>t to execute Phase 4: Beat Position Estimation on the full dataset.
Processes all audio segments from Phase 3 and generates beat/onset detection results.
"""

import sys
import logging
import time
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from phases.phase_04_beat_estimation import Phase4BeatEstimator


def setup_logging():
    """Setup logging for the script."""
    log_dir = Path("data/logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / f"run_phase04_{time.strftime('%Y%m%d_%H%M%S')}.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def check_prerequisites():
    """Check if Phase 3 outputs are available."""
    phase3_dir = Path("data/processed/phase3")
    
    required_dirs = [
        phase3_dir / "audio_segments",
        phase3_dir / "silence_maps"
    ]
    
    missing_dirs = [d for d in required_dirs if not d.exists()]
    
    if missing_dirs:
        print("❌ Missing required Phase 3 outputs:")
        for d in missing_dirs:
            print(f"   - {d}")
        print("\nPlease run Phase 3 first to generate the required input data.")
        return False
    
    # Check for segment files
    segment_files = list((phase3_dir / "audio_segments").glob("*_segment_*.npy"))
    if not segment_files:
        print("❌ No audio segment files found in Phase 3 outputs")
        return False
    
    print(f"✅ Found {len(segment_files)} audio segments ready for processing")
    return True


def main():
    """Main execution function."""
    
    print("="*70)
    print("🎵 PHASE 4: BEAT POSITION ESTIMATION")
    print("="*70)
    print("Detecting beat positions and onsets in audio segments...")
    print()
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Check prerequisites
    if not check_prerequisites():
        return False
    
    # Configuration for Phase 4
    config = {
        "sample_rate": 22050,
        "hop_length": 512,
        "detection_method": "combined",  # Use both librosa and madmom if available
        "use_tja_bpm": True,
        "onset_threshold": 0.3,
        "beat_confidence_threshold": 0.7,
        "tempo_confidence_threshold": 0.6,
        "max_beat_interval_variance": 0.3,
        "clustering_eps": 0.05,  # 50ms tolerance for beat clustering
        "visualization": True,
        "batch_size": 6,
        "max_processing_time": 30,
        "memory_limit_mb": 2048
    }
    
    try:
        # Initialize Phase 4 processor
        logger.info("Initializing Phase 4 Beat Estimator")
        estimator = Phase4BeatEstimator(config)
        
        # Display configuration
        print("Configuration:")
        print(f"  - Detection method: {config['detection_method']}")
        print(f"  - Sample rate: {config['sample_rate']} Hz")
        print(f"  - Hop length: {config['hop_length']}")
        print(f"  - Onset threshold: {config['onset_threshold']}")
        print(f"  - Beat confidence threshold: {config['beat_confidence_threshold']}")
        print(f"  - Visualization enabled: {config['visualization']}")
        print()
        
        # Process beat detection
        start_time = time.time()
        logger.info("Starting beat detection processing")
        
        results = estimator.process_beat_detection(
            input_dir=Path("data/processed/phase3"),
            output_dir=Path("data/processed/phase4")
        )
        
        processing_time = time.time() - start_time
        
        # Display results
        print("\n" + "="*70)
        print("📊 PROCESSING RESULTS")
        print("="*70)
        
        success_rate = results['processed_segments'] / results['total_segments'] * 100
        
        print(f"Total segments: {results['total_segments']}")
        print(f"Successfully processed: {results['processed_segments']}")
        print(f"Failed: {results['failed_segments']}")
        print(f"Success rate: {success_rate:.1f}%")
        print(f"Total processing time: {processing_time:.1f} seconds")
        print()
        
        if results['processed_segments'] > 0:
            print("Quality Metrics:")
            print(f"  - Average tempo: {results['avg_tempo']:.1f} BPM")
            print(f"  - Average beats per segment: {results['avg_beats_per_segment']:.1f}")
            
            if 'avg_processing_time_ms' in results:
                print(f"  - Average processing time: {results['avg_processing_time_ms']:.1f} ms/segment")
            
            if 'avg_quality_score' in results:
                print(f"  - Average quality score: {results['avg_quality_score']:.3f}")
            
            if results.get('tempo_accuracy', 0) > 0:
                print(f"  - Tempo accuracy: {results['tempo_accuracy']*100:.1f}%")
        
        print()
        
        # Display output information
        print("📁 Generated Output Files:")
        output_dir = Path("data/processed/phase4")
        
        beat_files = len(list((output_dir / "beat_positions").glob("*.json")))
        onset_files = len(list((output_dir / "onset_positions").glob("*.json")))
        tempo_files = len(list((output_dir / "tempo_analysis").glob("*.json")))
        validation_files = len(list((output_dir / "validation").glob("*.json")))
        
        print(f"  - Beat positions: {beat_files} files")
        print(f"  - Onset positions: {onset_files} files")
        print(f"  - Tempo analysis: {tempo_files} files")
        print(f"  - Validation results: {validation_files} files")
        
        if config['visualization']:
            viz_files = len(list((output_dir / "beat_visualizations").glob("*.png")))
            print(f"  - Visualizations: {viz_files} files")
        
        print(f"  - Processing report: beat_tracking_report.json")
        print()
        
        # Display error summary if any
        if results['processing_errors']:
            print("⚠️  Processing Errors:")
            error_types = {}
            for error in results['processing_errors']:
                error_type = error.get('error_type', 'Unknown')
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            for error_type, count in error_types.items():
                print(f"  - {error_type}: {count} occurrences")
            print()
        
        # Success message
        if success_rate >= 95:
            print("✅ Phase 4 completed successfully!")
        elif success_rate >= 80:
            print("⚠️  Phase 4 completed with some issues")
        else:
            print("❌ Phase 4 completed with significant issues")
        
        print()
        print("Next steps:")
        print("  - Review the processing report for detailed statistics")
        print("  - Check validation results for quality assessment")
        print("  - Proceed to Phase 5: Tempo Alignment & BPM Validation")
        print()
        
        logger.info(f"Phase 4 completed with {success_rate:.1f}% success rate")
        
        return success_rate >= 80  # Consider 80%+ success rate as acceptable
        
    except Exception as e:
        logger.error(f"Phase 4 processing failed: {e}")
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
